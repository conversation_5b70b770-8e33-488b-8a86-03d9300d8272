import { z } from "zod";
import { RAW_MATERIAL_STAUS, type ICreateVariation, type IRawMaterial, type IRawMaterialAddRequest, type IRawMaterialCreatePaylod, type IRawMaterialFormState, type IRawMaterialVariationAddRequest, } from "../models/IRawMaterial";
import { CSVProvider } from "$lib/csv-provider/repositories/CSVProvider";
import { showErrorToast } from "$lib/common/utils/common-utils";
import { PresenterProvider } from "$lib/PresenterProvider";
import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";

export abstract class RawMaterialUtils {

    static emptyFormState: IRawMaterialFormState = {
        rawMaterial: {
            category: null,
            unit: null,
            name: "",
            hsn: "",
            gstPercentage: 0,
        },
        variations: [],
    };

    static getEmptyVariation() {
        return {
            enabled: true,
            hash: "",
            name: "",
            sku: "",
            msq: 0,
            moq: 0,
            attributes: [],
            priceData: [],
        };
    }





    private static priceData = z.object({
        price: z.number().int().positive("Price must be a positive number"),
        supplierId: z.number().int().positive("Please select a valid supplier"),
    });

    private static variationSchema = z.object({
        name: z.string().min(3, "Name must be at least 3 characters long").max(100, "Name must be up to 100 characters long"),
        sku: z.string().min(3, "SKU must be at least 3 characters long").max(100, "SKU must be up to 100 characters long"),
        msq: z.number().nonnegative("MSQ must be positive"),
        moq: z.number().nonnegative("MOQ must be positive"),
        priceData: z.array(this.priceData).min(1, "At least one supplier is required"),
    });

    static creationSchema = z.object({
        name: z.string().min(3, 'Name must be at least 3 characters long').max(100, 'Name must be up to 100 characters long'),
        categoryId: z.number().int().positive('Please select a valid category'),
        unitId: z.number().int().positive('Please select a valid unit'),
        hsn: z.string().min(3, 'HSN must be at least 3 characters long').max(50, 'HSN must be up to 50 characters long'),
        gstPercentage: z.number().positive('GST Percentage must be positive'),
        // variations: z.array(this.variationSchema).min(1, "At least one variation is required"),
    });

    static validateCreate = (payload:IRawMaterialAddRequest ): ValidationErrors => {
        const errors: ValidationErrors = new Map();
        const result = RawMaterialUtils.creationSchema.safeParse(payload);
        if (!result.success) {
            result.error.issues.forEach((issue) => {
                errors.set(issue.path[0].toString(), issue.message);
            });
        }
        return errors;
    }

    static validateVariations = (payload: IRawMaterialVariationAddRequest[]): IndexedValidationErrors => {
        const errors: IndexedValidationErrors = new Map();

        let result;
        let item;
        for (let i = 0; i < payload.length; i++) {
            item = payload[i];

            result = this.variationSchema.safeParse(item);
            console.log("variation schema", result);

            if (!result.success) {
                result.error.issues.forEach((issue) => {
                    const fieldName = issue.path.join('.').replace(/\.\d+\./, '.');
                    if (!errors.has(i)) {
                        errors.set(i, [{ fieldName, errorMessage: issue.message }]);
                    } else {
                        errors.get(i)!.push({ fieldName, errorMessage: issue.message });
                    }
                });
                break;
            }
        }
        return errors;
    }
}

let keyMap: { [key: string]: string } = {
    'Name': 'name',
    // 'Unit Id': 'unitId',
    'Unit Name': 'unitName',
    // 'Category Id': 'categoryId',
    'Category Name': 'categoryName',
    'Sku': 'sku',
    'Msq': 'msq',
    'Hsn': 'hsn',
    'Gst Percentage': 'gstPercentage',
    // 'Price': 'price',
    'Status': 'status',
}

export const downloadCsv = async (data: IRawMaterial[], fileName: string) => {
    if (data.length === 0) {
        showErrorToast("Please choose data")
        return;
    }
    await new CSVProvider().save(data, fileName, keyMap);
}

export const downloadAllCsv = async (startDate: Date, endDate: Date) => {
    const response = await PresenterProvider.rawMaterialPresenter.getAll(1, 2, startDate, endDate)
    if (response.success && response.data.data && response.data.data.length > 0) {
        await new CSVProvider().save(response.data.data, "raw-materials", keyMap);
    } else {
        showErrorToast("No data found")
    }
}