<script lang="ts">
    import type { IndexedValidationErrors, ValidationErrors } from "$lib/common/utils/types";
    import { CloseButton, Input } from "flowbite-svelte";
    import { RawMaterialUtils } from "../utils/RawMaterialUtils";
    import SupplierSearch from "$lib/supplier/components/SupplierSearch.svelte";
    import type { IRawMaterialFormState } from "../models/IRawMaterial";
    import { showErrorToast } from "$lib/common/utils/common-utils";
    import type { ISupplier } from "$lib/supplier/models/ISupplier";
    import AttributesSelection from "$lib/common/components/AttributesSelection.svelte";

    export let formState: IRawMaterialFormState;
    export let variationErrors: IndexedValidationErrors = new Map();
    export let validationErrors: ValidationErrors = new Map();

     const handleMsq = (e: Event, index: number) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        formState.variations[index].msq = isNaN(value) ? 0 : value;
    };

      const handleMOQ = (e: Event, index: number) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        formState.variations[index].moq = isNaN(value) ? 0 : value;
    };

    const assignSupplier = (supplier: ISupplier, VariationIndex: number, index: number) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }
        formState.variations[VariationIndex].priceData[index].supplier = supplier;
    };

    const handlePrice = (e: any, variationIndex: number, index: number) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }
        const value = parseFloat((e.target as HTMLInputElement).value);
        formState.variations[variationIndex].priceData[index].price = isNaN(value) ? 0 : value;
    };

    const handleAttributeSelection = (
        data: { attribute: any; attributeValue: any },
        variationIndex: number
    ) => {
        if (!formState.variations) {
            return showErrorToast("Please add variation first");
        }

        // Check if this attribute is already added to this variation
        const existingAttributeIndex = formState.variations[variationIndex].attributes.findIndex(
            (attr) => attr.attributeValueId === data.attributeValue.id
        );

        if (existingAttributeIndex === -1) {
            // Add new attribute
            formState.variations[variationIndex].attributes.push({
                attributeValueId: data.attributeValue.id,
            });
            formState.variations = formState.variations; // Trigger reactivity
        }
    };

    const removeAttribute = (variationIndex: number, attributeValueId: number) => {
        if (!formState.variations) {
            return;
        }

        formState.variations[variationIndex].attributes = formState.variations[
            variationIndex
        ].attributes.filter((attr) => attr.attributeValueId !== attributeValueId);
        formState.variations = formState.variations; // Trigger reactivity
    };

</script>

<table class="w-full text-left text-sm text-gray-500 dark:text-gray-400 rtl:text-right">
    <thead
        class="bg-gray-200 text-base uppercase text-gray-700 dark:bg-gray-700 dark:text-gray-400"
    >
        <tr>
            <th scope="col" class="px-6 py-3 text-sm">Sr. No.</th>
            <th scope="col" class="px-6 py-3 text-sm">Name</th>
            <th scope="col" class="px-6 py-3 text-sm">SKU</th>
            <th scope="col" class="px-6 py-3 text-sm">MSQ</th>
            <th scope="col" class="px-6 py-3 text-sm">MOQ</th>
            <th scope="col" class="px-6 py-3 text-sm">Actions</th>
        </tr>
    </thead>
    <tbody>
        {#each formState.variations as variation, index}
            <tr
                class="border-b bg-white text-gray-600 hover:bg-gray-50 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-600"
            >
                <td class="px-6 py-3">{index + 1}.</td>
                <td class="px-4 py-2">
                    <div>
                        <Input
                            type="text"
                            id="name"
                            placeholder="Name"
                            class="uppercase dark:bg-primary-700 w-[150px] h-[40px] {validationErrors.has(
                                'name'
                            )
                                ? 'border-red-500'
                                : ''}"
                            bind:value={variation.name}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "name")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "name")!.errorMessage}
                            </p>
                        {/if}
                    </div>
                </td>
                <td class="px-4 py-2">
                    <div>
                        <Input
                            type="text"
                            id="sku"
                            placeholder="SKU"
                            class="uppercase dark:bg-primary-700 w-[150px] h-[40px] {validationErrors.has(
                                'sku'
                            )
                                ? 'border-red-500'
                                : ''}"
                            bind:value={variation.sku}
                        />
                        {#if variationErrors.has(index) && variationErrors
                                .get(index)!
                                .find((item) => item.fieldName === "sku")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {variationErrors
                                    .get(index)!
                                    .find((item) => item.fieldName === "sku")!.errorMessage}
                            </p>
                        {/if}
                    </div>
                </td>
                <td class="px-4 py-2">
                    <div>
                        <Input
                            type="text"
                            id="msq"
                            placeholder="MSQ"
                            class="dark:bg-primary-700  w-[150px] h-[40px] {validationErrors.has(
                                'msq'
                            )
                                ? 'border-red-500'
                                : ''}"
                            value={variation.msq}
                            on:input={(e) => handleMsq(e, index)}
                        />
                        {#if validationErrors.has("msq")}
                            <p class="pt-2 font-serif text-[14px] italic text-red-500">
                                {validationErrors.get("msq")}
                            </p>
                        {/if}
                    </div>
                </td>
                <td class="px-4 py-2">
                    <Input
                        class={" w-[200px] h-[40px]"}
                        placeholder={"MOQ"}
                        value={variation.moq}
                        on:change={(e) => {
                            handleMOQ(e, index);
                        }}
                    />
                </td>
                <td class="px-4 py-2">
                    <button
                        type="button"
                        class="text-red-600 hover:text-red-800 font-medium"
                        on:click={() => {
                            formState.variations?.splice(index, 1);
                            formState.variations = formState.variations; // Trigger reactivity
                        }}
                    >
                        Delete
                    </button>
                </td>
            </tr>

            <tr>
                <td colspan="6" class="p-4 bg-gray-100 dark:bg-gray-700">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold mb-2">Attributes</h3>
                        <AttributesSelection
                            onSelect={(data) => {
                                handleAttributeSelection(data, index);
                            }}
                        />
                    </div>

                    <div class="mb-4">
                        <h3 class="text-lg font-semibold mb-2">Supplier Information</h3>

                        <table class="w-full border border-gray-300 dark:border-gray-600">
                            <thead
                                class="bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300"
                            >
                                <tr>
                                    <th class="px-4 py-2">SR No.</th>
                                    <th class="px-4 py-2">Supplier</th>
                                    <th class="px-4 py-2">Price</th>
                                    <th class="px-4 py-2">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {#if variation.priceData && variation.priceData.length > 0}
                                    {#each variation.priceData as priceItem, supplierIndex}
                                        <tr
                                            class="border-b bg-white text-gray-600 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400"
                                        >
                                            <td class="px-4 py-2">
                                                {supplierIndex + 1}
                                            </td>
                                            <td class="px-4 py-2">
                                                <SupplierSearch
                                                    selected={priceItem.supplier}
                                                    onSelected={(data) => {
                                                        assignSupplier(data, index, supplierIndex);
                                                    }}
                                                    isLabel={false}
                                                />
                                                {#if variationErrors.has(index) && variationErrors
                                                        .get(index)!
                                                        .find((item) => item.fieldName === "priceData.supplierId")}
                                                    <p
                                                        class="pt-2 font-serif text-[14px] italic text-red-500"
                                                    >
                                                        {variationErrors
                                                            .get(index)!
                                                            .find(
                                                                (item) =>
                                                                    item.fieldName ===
                                                                    "priceData.supplierId"
                                                            )!.errorMessage}
                                                    </p>
                                                {/if}
                                            </td>
                                            <td class="px-4 py-2">
                                                <Input
                                                    class="w-[200px] h-[40px]"
                                                    placeholder="Price"
                                                    value={priceItem.price}
                                                    on:change={(e) => {
                                                        handlePrice(e, index, supplierIndex);
                                                    }}
                                                />
                                                {#if variationErrors.has(index) && variationErrors
                                                        .get(index)!
                                                        .find((item) => item.fieldName === "priceData.price")}
                                                    <p
                                                        class="pt-2 font-serif text-[14px] italic text-red-500"
                                                    >
                                                        {variationErrors
                                                            .get(index)!
                                                            .find(
                                                                (item) =>
                                                                    item.fieldName ===
                                                                    "priceData.price"
                                                            )!.errorMessage}
                                                    </p>
                                                {/if}
                                            </td>
                                            <td class="px-4 py-2">
                                                <CloseButton
                                                    class="w-content"
                                                    on:click={() => {
                                                        if (variation.priceData) {
                                                            variation.priceData =
                                                                variation.priceData.filter(
                                                                    (_, i) => i !== supplierIndex
                                                                );
                                                        }
                                                    }}
                                                />
                                            </td>
                                        </tr>
                                    {/each}
                                {:else}
                                    <tr>
                                        <td colspan="4" class="px-4 py-4 text-center">
                                            No suppliers added yet. Click "Add New Supplier" below
                                            to add one.
                                        </td>
                                    </tr>
                                {/if}
                            </tbody>
                        </table>

                        <div class="mt-3 flex justify-end">
                            <button
                                type="button"
                                class="flex items-center px-4 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-md transition-colors"
                                on:click={() => {
                                    if (!variation.priceData) {
                                        variation.priceData = [];
                                    }
                                    variation.priceData = [
                                        ...variation.priceData,
                                        { price: 0, supplier: null },
                                    ];
                                }}
                            >
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="h-5 w-5 mr-1"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M12 4v16m8-8H4"
                                    />
                                </svg>
                                Add New Supplier
                            </button>
                        </div>
                    </div>
                </td>
            </tr>
        {/each}
    </tbody>
</table>
